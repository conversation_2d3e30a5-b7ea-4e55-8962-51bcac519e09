// 权限管理系统
export interface Permission {
  code: string
  name: string
  description: string
  module: string
}

export interface Role {
  id: number
  name: string
  description: string
  permissions: string[]
}

export interface User {
  studentId: string
  name: string
  role: string
  permissions?: string[]
}

// 系统权限定义
export const PERMISSIONS: Permission[] = [
  { code: 'user.manage', name: '用户管理', description: '创建、编辑、删除用户账户', module: 'system' },
  { code: 'role.manage', name: '角色管理', description: '管理角色和权限配置', module: 'system' },
  { code: 'system.config', name: '系统配置', description: '修改系统参数和配置', module: 'system' },
  { code: 'student.manage', name: '学生管理', description: '管理学生信息和学籍', module: 'student' },
  { code: 'student.view', name: '查看学生', description: '查看学生基本信息', module: 'student' },
  { code: 'grade.manage', name: '成绩管理', description: '管理所有学生成绩', module: 'grade' },
  { code: 'grade.input', name: '成绩录入', description: '录入和修改成绩', module: 'grade' },
  { code: 'grade.view', name: '查看成绩', description: '查看个人成绩信息', module: 'grade' },
  { code: 'course.manage', name: '课程管理', description: '管理课程信息和安排', module: 'course' },
  { code: 'course.view', name: '查看课程', description: '查看课程信息', module: 'course' },
  { code: 'course.select', name: '选课', description: '进行课程选择', module: 'course' },
  { code: 'profile.view', name: '个人资料', description: '查看和修改个人信息', module: 'profile' },
  { code: 'data.export', name: '数据导出', description: '导出系统数据', module: 'system' },
  { code: 'log.view', name: '日志查看', description: '查看系统操作日志', module: 'system' },
  { code: 'report.view', name: '报表查看', description: '查看统计报表', module: 'report' }
]

// 系统角色定义
export const ROLES: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    description: '拥有系统所有权限，可以管理所有功能模块',
    permissions: [
      'user.manage', 'role.manage', 'system.config', 'data.export', 'log.view',
      'student.manage', 'student.view', 'grade.manage', 'grade.input', 'grade.view',
      'course.manage', 'course.view', 'course.select', 'profile.view', 'report.view'
    ]
  },
  {
    id: 2,
    name: '教务管理员',
    description: '负责学生信息和成绩管理，课程安排等教务工作',
    permissions: [
      'student.manage', 'student.view', 'grade.manage', 'grade.input', 'grade.view',
      'course.manage', 'course.view', 'report.view', 'data.export'
    ]
  },
  {
    id: 3,
    name: '教师',
    description: '可以查看和录入所授课程的学生成绩',
    permissions: [
      'student.view', 'grade.input', 'grade.view', 'course.view', 'profile.view'
    ]
  },
  {
    id: 4,
    name: '学生',
    description: '可以查看个人信息和成绩，进行选课等操作',
    permissions: [
      'profile.view', 'grade.view', 'course.view', 'course.select'
    ]
  }
]

// 权限检查类
export class PermissionManager {
  private static instance: PermissionManager
  private currentUser: User | null = null

  private constructor() {}

  public static getInstance(): PermissionManager {
    if (!PermissionManager.instance) {
      PermissionManager.instance = new PermissionManager()
    }
    return PermissionManager.instance
  }

  // 设置当前用户
  public setCurrentUser(user: User): void {
    this.currentUser = user
    // 根据角色获取权限
    const role = ROLES.find(r => r.name === user.role)
    if (role) {
      this.currentUser.permissions = role.permissions
    }
  }

  // 获取当前用户
  public getCurrentUser(): User | null {
    return this.currentUser
  }

  // 检查是否有指定权限
  public hasPermission(permissionCode: string): boolean {
    if (!this.currentUser || !this.currentUser.permissions) {
      return false
    }
    return this.currentUser.permissions.includes(permissionCode)
  }

  // 检查是否有任一权限
  public hasAnyPermission(permissionCodes: string[]): boolean {
    if (!this.currentUser || !this.currentUser.permissions) {
      return false
    }
    return permissionCodes.some(code => this.currentUser!.permissions!.includes(code))
  }

  // 检查是否有所有权限
  public hasAllPermissions(permissionCodes: string[]): boolean {
    if (!this.currentUser || !this.currentUser.permissions) {
      return false
    }
    return permissionCodes.every(code => this.currentUser!.permissions!.includes(code))
  }

  // 获取用户角色
  public getUserRole(): string {
    return this.currentUser?.role || '未知'
  }

  // 是否是管理员
  public isAdmin(): boolean {
    return this.hasPermission('user.manage') || this.hasPermission('system.config')
  }

  // 是否是教务管理员
  public isAcademicAdmin(): boolean {
    return this.hasPermission('student.manage') && this.hasPermission('grade.manage')
  }

  // 是否是教师
  public isTeacher(): boolean {
    return this.hasPermission('grade.input') && !this.hasPermission('student.manage')
  }

  // 是否是学生
  public isStudent(): boolean {
    return this.currentUser?.role === '学生'
  }

  // 获取用户权限列表
  public getUserPermissions(): string[] {
    return this.currentUser?.permissions || []
  }

  // 根据学号判断角色
  public static determineUserRole(studentId: string): string {
    // 超级管理员账号：2024140520（徐超管）
    if (studentId === '2024140520') {
      return '超级管理员'
    }

    // 教务管理员账号：明确指定的账号列表（不包括马豪）
    if (['2024140521', '2024140522', '2024140523', '2024140524', '2024140525'].includes(studentId)) {
      return '教务管理员'
    }

    // 教师账号：以2024141开头的账号
    if (studentId.startsWith('2024141')) {
      return '教师'
    }

    // 其他都是学生（包括马豪2024010001等所有普通学生）
    return '学生'
  }
}

// 权限装饰器函数
export function requirePermission(permissionCode: string) {
  return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    descriptor.value = function(...args: any[]) {
      const pm = PermissionManager.getInstance()
      if (!pm.hasPermission(permissionCode)) {
        throw new Error(`权限不足：需要 ${permissionCode} 权限`)
      }
      return method.apply(this, args)
    }
  }
}

// 权限检查工具函数
export function checkPermission(permissionCode: string): boolean {
  return PermissionManager.getInstance().hasPermission(permissionCode)
}

export function checkAnyPermission(permissionCodes: string[]): boolean {
  return PermissionManager.getInstance().hasAnyPermission(permissionCodes)
}

export function checkAllPermissions(permissionCodes: string[]): boolean {
  return PermissionManager.getInstance().hasAllPermissions(permissionCodes)
}

// 获取权限管理器实例
export const permissionManager = PermissionManager.getInstance()
