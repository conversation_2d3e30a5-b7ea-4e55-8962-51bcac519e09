<template>
  <div class="users-view">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统中的所有用户账户</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>用户列表</h2>
        <div class="header-actions">
          <button class="btn btn-primary" @click="handleAddUser">
            <span class="btn-icon">➕</span>
            添加用户
          </button>
          <button class="btn btn-secondary" @click="handleImportUsers">
            <span class="btn-icon">📥</span>
            批量导入
          </button>
        </div>
      </div>

      <div class="search-bar">
        <input 
          type="text" 
          placeholder="搜索用户名、姓名或邮箱..." 
          class="search-input"
          v-model="searchKeyword"
        >
        <button class="search-btn" @click="handleSearch">
          <span class="btn-icon">🔍</span>
        </button>
      </div>

      <div class="users-table">
        <table>
          <thead>
            <tr>
              <th>用户ID</th>
              <th>用户名</th>
              <th>姓名</th>
              <th>角色</th>
              <th>邮箱</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in filteredUsers" :key="user.id">
              <td>{{ user.id }}</td>
              <td>{{ user.username }}</td>
              <td>{{ user.name }}</td>
              <td>
                <span class="role-badge" :class="user.role">
                  {{ getRoleName(user.role) }}
                </span>
              </td>
              <td>{{ user.email }}</td>
              <td>
                <span class="status-badge" :class="user.status">
                  {{ user.status === 'active' ? '正常' : '禁用' }}
                </span>
              </td>
              <td>{{ user.createTime }}</td>
              <td>
                <div class="action-buttons">
                  <button class="btn-small btn-edit" @click="handleEditUser(user)">编辑</button>
                  <button class="btn-small btn-delete" @click="handleDeleteUser(user)">删除</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <span>共 {{ totalUsers }} 个用户</span>
        <div class="pagination-controls">
          <button class="btn-small" @click="prevPage" :disabled="currentPage === 1">上一页</button>
          <span>第 {{ currentPage }} / {{ totalPages }} 页</span>
          <button class="btn-small" @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 编辑用户对话框 -->
    <div v-if="showEditDialog" class="modal-overlay" @click="closeEditDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingUser.id ? '编辑用户' : '添加用户' }}</h3>
          <button class="close-btn" @click="closeEditDialog">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveUser">
            <div class="form-group">
              <label>用户名</label>
              <input
                type="text"
                v-model="editingUser.username"
                :readonly="editingUser.id > 0"
                class="form-input"
                placeholder="请输入用户名"
                required
              >
            </div>
            <div class="form-group">
              <label>姓名</label>
              <input
                type="text"
                v-model="editingUser.name"
                class="form-input"
                placeholder="请输入姓名"
                required
              >
            </div>
            <div class="form-group">
              <label>角色</label>
              <select v-model="editingUser.role" class="form-input" required>
                <option value="">请选择角色</option>
                <option value="superadmin">超级管理员</option>
                <option value="admin">管理员</option>
                <option value="teacher">教师</option>
                <option value="student">学生</option>
              </select>
            </div>
            <div class="form-group">
              <label>邮箱</label>
              <input
                type="email"
                v-model="editingUser.email"
                class="form-input"
                placeholder="请输入邮箱"
                required
              >
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model="editingUser.status" class="form-input" required>
                <option value="active">正常</option>
                <option value="inactive">禁用</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeEditDialog">取消</button>
          <button type="button" class="btn btn-primary" @click="saveUser">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showEditDialog = ref(false)
const editingUser = ref({
  id: 0,
  username: '',
  name: '',
  role: '',
  email: '',
  status: 'active',
  createTime: ''
})

// 用户数据 - 支持本地存储持久化
const users = ref([])

// 初始化用户数据
const initializeUsers = () => {
  // 尝试从localStorage加载用户数据
  const savedUsers = localStorage.getItem('systemUsers')
  if (savedUsers) {
    users.value = JSON.parse(savedUsers)
  } else {
    // 默认用户数据
    users.value = [
      {
        id: 1,
        username: '2024140520',
        name: '系统管理员',
        role: 'superadmin',
        email: '<EMAIL>',
        status: 'active',
        createTime: '2024-01-01'
      },
      {
        id: 2,
        username: '2024010001',
        name: '马豪',
        role: 'student',
        email: '马豪@jou.edu.cn',
        status: 'active',
        createTime: '2024-09-01'
      },
      {
        id: 3,
        username: 'T001',
        name: '李教授',
        role: 'teacher',
        email: '<EMAIL>',
        status: 'active',
        createTime: '2024-02-15'
      },
      {
        id: 4,
        username: '2024010002',
        name: '董强',
        role: 'student',
        email: '董强@jou.edu.cn',
        status: 'active',
        createTime: '2024-09-01'
      }
    ]
    saveUsers()
  }
}

// 保存用户数据到localStorage
const saveUsers = () => {
  localStorage.setItem('systemUsers', JSON.stringify(users.value))
}

// 计算属性
const filteredUsers = computed(() => {
  if (!searchKeyword.value) return users.value
  return users.value.filter(user => 
    user.username.includes(searchKeyword.value) ||
    user.name.includes(searchKeyword.value) ||
    user.email.includes(searchKeyword.value)
  )
})

const totalUsers = computed(() => filteredUsers.value.length)
const totalPages = computed(() => Math.ceil(totalUsers.value / pageSize.value))

// 方法
const getRoleName = (role: string) => {
  const roleMap = {
    superadmin: '超级管理员',
    admin: '管理员',
    teacher: '教师',
    student: '学生'
  }
  return roleMap[role] || '未知'
}

const handleAddUser = () => {
  editingUser.value = {
    id: 0,
    username: '',
    name: '',
    role: '',
    email: '',
    status: 'active',
    createTime: ''
  }
  showEditDialog.value = true
}

const handleImportUsers = () => {
  alert('批量导入功能\n\n支持Excel文件导入用户数据\n格式：用户名,姓名,角色,邮箱')
}

const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
}

const handleEditUser = (user: any) => {
  editingUser.value = { ...user }
  showEditDialog.value = true
}

const handleDeleteUser = (user: any) => {
  if (user.role === 'superadmin') {
    alert('超级管理员账号不能删除！')
    return
  }

  if (confirm(`确定要删除用户 "${user.name}" 吗？\n\n此操作不可恢复！`)) {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      saveUsers()
      alert('用户删除成功！')
    }
  }
}

const closeEditDialog = () => {
  showEditDialog.value = false
  editingUser.value = {
    id: 0,
    username: '',
    name: '',
    role: '',
    email: '',
    status: 'active',
    createTime: ''
  }
}

const saveUser = () => {
  // 验证必填字段
  if (!editingUser.value.username || !editingUser.value.name || !editingUser.value.role || !editingUser.value.email) {
    alert('请填写所有必填字段！')
    return
  }

  // 验证用户名唯一性（编辑时排除自己）
  const existingUser = users.value.find(u => u.username === editingUser.value.username && u.id !== editingUser.value.id)
  if (existingUser) {
    alert('用户名已存在，请使用其他用户名！')
    return
  }

  if (editingUser.value.id === 0) {
    // 添加新用户
    const newId = Math.max(...users.value.map(u => u.id), 0) + 1
    const newUser = {
      ...editingUser.value,
      id: newId,
      createTime: new Date().toISOString().split('T')[0]
    }
    users.value.push(newUser)
    alert('用户添加成功！')
  } else {
    // 更新现有用户
    const index = users.value.findIndex(u => u.id === editingUser.value.id)
    if (index > -1) {
      users.value[index] = { ...editingUser.value }
      alert('用户信息更新成功！')
    }
  }

  saveUsers()
  closeEditDialog()
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

onMounted(() => {
  console.log('用户管理页面已加载')
  initializeUsers()
})
</script>

<style scoped>
.users-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
  background: #e3f2fd;
}

.search-bar {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  gap: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.users-table {
  overflow-x: auto;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.users-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.role-badge,
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.role-badge.superadmin {
  background: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffb74d;
}

.role-badge.admin {
  background: #e3f2fd;
  color: #1976d2;
}

.role-badge.teacher {
  background: #f3e5f5;
  color: #7b1fa2;
}

.role-badge.student {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.active {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.inactive {
  background: #ffebee;
  color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-edit {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-delete {
  background: #ffebee;
  color: #d32f2f;
}

.pagination {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f5f5f5;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input:readonly {
  background: #f5f7fa;
  color: #666;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
</style>
