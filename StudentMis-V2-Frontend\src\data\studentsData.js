// 江苏海洋大学5000名学生数据
// 根据SQL数据转换为前端可用格式

export const studentsData = [
  // 海洋生命与水产学院 (学院代码: 01)
  {
    id: 1,
    studentId: '2024010001',
    name: '马豪',
    gender: 'MALE',
    birthDate: '2004-08-13',
    phone: '18498784308',
    email: '马豪@jou.edu.cn',
    major: '海洋科学',
    className: '海洋2401班',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 45,
    requiredCredits: 35,
    electiveCredits: 10,
    gpa: 3.2,
    failedCourses: []
  },
  {
    id: 2,
    studentId: '2024010002',
    name: '董强',
    gender: 'MALE',
    birthDate: '2002-10-28',
    phone: '18769016639',
    email: '董强@jou.edu.cn',
    major: '海洋科学',
    className: '海洋2401班',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 42,
    requiredCredits: 32,
    electiveCredits: 10,
    gpa: 3.0,
    failedCourses: []
  },
  {
    id: 3,
    studentId: '2024010003',
    name: '萧阳',
    gender: 'MALE',
    birthDate: '2002-04-23',
    phone: '15201187728',
    email: '萧阳@jou.edu.cn',
    major: '海洋科学',
    className: '海洋2401班',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 48,
    requiredCredits: 38,
    electiveCredits: 10,
    gpa: 3.5,
    failedCourses: []
  },
  {
    id: 4,
    studentId: '2024010004',
    name: '董芳',
    gender: 'FEMALE',
    birthDate: '2001-03-03',
    phone: '15895369513',
    email: '董芳@jou.edu.cn',
    major: '海洋科学',
    className: '海洋2401班',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 46,
    requiredCredits: 36,
    electiveCredits: 10,
    gpa: 3.3,
    failedCourses: []
  },
  {
    id: 5,
    studentId: '2024010005',
    name: '孙娜',
    gender: 'FEMALE',
    birthDate: '2003-11-22',
    phone: '15119692615',
    email: '孙娜@jou.edu.cn',
    major: '海洋科学',
    className: '海洋2401班',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 44,
    requiredCredits: 34,
    electiveCredits: 10,
    gpa: 3.1,
    failedCourses: []
  },
  // 管理员账号
  {
    id: 6,
    studentId: '2024140520',
    name: '系统管理员',
    gender: 'MALE',
    birthDate: '1990-01-01',
    phone: '18626425051',
    email: '<EMAIL>',
    major: '系统管理',
    className: '管理员',
    grade: 2024,
    admissionDate: '2024-09-01',
    status: 'ACTIVE',
    totalCredits: 160,
    requiredCredits: 120,
    electiveCredits: 40,
    gpa: 4.0,
    failedCourses: []
  }
]

// 生成更多学生数据的函数
export const generateMoreStudents = () => {
  const majors = [
    '海洋科学', '水产养殖学', '海洋渔业科学与技术', '食品科学与工程',
    '计算机科学与技术', '软件工程', '网络工程', '数据科学与大数据技术',
    '机械设计制造及其自动化', '船舶与海洋工程', '港口航道与海岸工程',
    '土木工程', '工程管理', '建筑学', '环境工程',
    '电子信息工程', '通信工程', '自动化', '电气工程及其自动化',
    '国际经济与贸易', '工商管理', '会计学', '市场营销', '金融学',
    '英语', '日语', '朝鲜语', '汉语言文学',
    '数学与应用数学', '应用物理学', '化学', '生物科学',
    '法学', '行政管理', '公共事业管理'
  ]

  const colleges = [
    '海洋生命与水产学院', '计算机工程学院', '机械与海洋工程学院', 
    '土木与港海工程学院', '电子工程学院', '商学院', '外国语学院',
    '理学院', '法学院', '文学院', '体育学院'
  ]

  const surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎', '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜', '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆', '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史', '顾', '侯', '邵', '孟', '龙', '万', '段', '雷', '钱', '汤', '尹', '黎', '易', '常', '武', '乔', '贺', '赖', '龚', '文']

  const givenNames = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞', '平', '刚', '桂英', '华', '建华', '建国', '建军', '春梅', '海燕', '雪梅', '秀珍', '志强', '志明', '秀云', '桂兰', '志华', '玉兰', '桂珍', '春兰', '玉珍', '春华', '红梅', '玉华', '春花', '桂花', '玉梅', '志勇', '秀梅', '建平', '桂芳', '丽娟', '秀芳', '国强', '桂英', '志刚', '秀英', '丽华', '志军', '桂香', '志伟', '秀华', '建民', '志国', '春霞', '建设', '志平', '志敏', '桂莲', '志华', '建华', '志强', '桂英', '志明', '秀云', '桂兰', '志华', '玉兰', '桂珍', '春兰', '玉珍', '春华', '红梅', '玉华', '春花', '桂花', '玉梅', '志勇', '秀梅', '建平', '桂芳', '丽娟', '秀芳', '国强', '桂英', '志刚']

  const moreStudents = []
  
  // 生成剩余的学生数据
  for (let i = 7; i <= 5000; i++) {
    const collegeIndex = Math.floor((i - 1) / 500) % colleges.length
    const majorIndex = Math.floor(Math.random() * majors.length)
    const year = 2024 - Math.floor(Math.random() * 4) // 2021-2024年级
    const collegeCode = String(collegeIndex + 1).padStart(2, '0')
    const majorCode = String(majorIndex + 1).padStart(2, '0')
    const classNum = Math.floor(Math.random() * 10) + 1
    const studentNum = String(i).padStart(2, '0')
    
    const surname = surnames[Math.floor(Math.random() * surnames.length)]
    const givenName = givenNames[Math.floor(Math.random() * givenNames.length)]
    const name = surname + givenName
    
    const gender = Math.random() > 0.5 ? 'MALE' : 'FEMALE'
    const birthYear = 2001 + Math.floor(Math.random() * 5)
    const birthMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
    const birthDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
    
    const phone = '1' + Math.floor(Math.random() * 9 + 3) + Math.floor(Math.random() * 1000000000).toString().padStart(9, '0')
    
    // 根据年级计算学分和绩点
    const gradeLevel = 2024 - year + 1
    const baseCredits = gradeLevel * 40
    const variation = Math.floor(Math.random() * 20) - 10
    const totalCredits = Math.max(0, baseCredits + variation)
    const requiredCredits = Math.floor(totalCredits * 0.75)
    const electiveCredits = totalCredits - requiredCredits
    
    // 生成绩点 (大部分学生在2.5-3.8之间)
    const gpaBase = 2.5 + Math.random() * 1.3
    const gpa = Math.round(gpaBase * 100) / 100
    
    // 少数学生有挂科
    const failedCourses = Math.random() < 0.15 ? ['高等数学'] : []
    if (Math.random() < 0.05) failedCourses.push('大学英语')
    
    const student = {
      id: i,
      studentId: `${year}${collegeCode}${String(i).padStart(4, '0')}`,
      name: name,
      gender: gender,
      birthDate: `${birthYear}-${birthMonth}-${birthDay}`,
      phone: phone,
      email: `${name}@jou.edu.cn`,
      major: majors[majorIndex],
      className: `${majors[majorIndex].substring(0, 4)}${String(year).substring(2)}${String(classNum).padStart(2, '0')}班`,
      grade: year,
      admissionDate: `${year}-09-01`,
      status: Math.random() < 0.95 ? 'ACTIVE' : (Math.random() < 0.5 ? 'GRADUATED' : 'SUSPENDED'),
      totalCredits: totalCredits,
      requiredCredits: requiredCredits,
      electiveCredits: electiveCredits,
      gpa: gpa,
      failedCourses: failedCourses
    }
    
    moreStudents.push(student)
  }
  
  return moreStudents
}

// 导出完整的学生数据
export const getAllStudents = () => {
  return [...studentsData, ...generateMoreStudents()]
}

// 按专业分组的学生数据
export const getStudentsByMajor = () => {
  const allStudents = getAllStudents()
  const grouped = {}
  
  allStudents.forEach(student => {
    if (!grouped[student.major]) {
      grouped[student.major] = []
    }
    grouped[student.major].push(student)
  })
  
  return grouped
}

// 按年级分组的学生数据
export const getStudentsByGrade = () => {
  const allStudents = getAllStudents()
  const grouped = {}
  
  allStudents.forEach(student => {
    if (!grouped[student.grade]) {
      grouped[student.grade] = []
    }
    grouped[student.grade].push(student)
  })
  
  return grouped
}

// 统计信息
export const getStudentStats = () => {
  const allStudents = getAllStudents()
  
  return {
    total: allStudents.length,
    active: allStudents.filter(s => s.status === 'ACTIVE').length,
    graduated: allStudents.filter(s => s.status === 'GRADUATED').length,
    suspended: allStudents.filter(s => s.status === 'SUSPENDED').length,
    averageGpa: (allStudents.reduce((sum, s) => sum + s.gpa, 0) / allStudents.length).toFixed(2),
    totalCredits: allStudents.reduce((sum, s) => sum + s.totalCredits, 0)
  }
}
