<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ userInfo.name }}</h1>
          <p>{{ welcomeMessage }}</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalStudents }}</div>
              <div class="stat-label">在校学生</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">📚</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalCourses }}</div>
              <div class="stat-label">开设课程</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">📊</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalGrades }}</div>
              <div class="stat-label">成绩记录</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 快速操作卡片 -->
      <div class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-cards">
          <div class="action-card" @click="navigateTo('/students')">
            <div class="card-icon student-icon">👨‍🎓</div>
            <h3>学生管理</h3>
            <p>查看和管理学生信息</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/grades')">
            <div class="card-icon grade-icon">📝</div>
            <h3>成绩管理</h3>
            <p>录入和查询学生成绩</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/analytics')">
            <div class="card-icon analytics-icon">📈</div>
            <h3>数据分析</h3>
            <p>查看统计报表和分析</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/settings')">
            <div class="card-icon settings-icon">⚙️</div>
            <h3>系统设置</h3>
            <p>配置系统参数和权限</p>
            <div class="card-arrow">→</div>
          </div>
        </div>
      </div>

      <!-- 数据概览 -->
      <div class="data-overview">
        <div class="overview-left">
          <h2 class="section-title">成绩分布概览</h2>
          <div class="chart-container">
            <div class="chart-placeholder">
              <div class="chart-icon">📊</div>
              <p>成绩分布图表</p>
              <small>显示各分数段学生分布情况</small>
            </div>
          </div>
        </div>
        
        <div class="overview-right">
          <h2 class="section-title">最近活动</h2>
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon" :class="activity.type">
                {{ activity.icon }}
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知公告 -->
      <div class="notifications-section">
        <h2 class="section-title">通知公告</h2>
        <div class="notification-cards">
          <div class="notification-card important">
            <div class="notification-header">
              <span class="notification-badge">重要</span>
              <span class="notification-date">2024-06-19</span>
            </div>
            <h3>期末考试成绩录入通知</h3>
            <p>请各任课教师在6月25日前完成期末考试成绩录入工作...</p>
          </div>
          <div class="notification-card normal">
            <div class="notification-header">
              <span class="notification-badge">通知</span>
              <span class="notification-date">2024-06-18</span>
            </div>
            <h3>系统维护公告</h3>
            <p>系统将于本周六晚上进行例行维护，预计维护时间2小时...</p>
          </div>
          <div class="notification-card normal">
            <div class="notification-header">
              <span class="notification-badge">提醒</span>
              <span class="notification-date">2024-06-17</span>
            </div>
            <h3>学期总结报告提交</h3>
            <p>请各学院在月底前提交本学期教学总结报告...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 用户信息 - 从localStorage获取当前登录用户信息
const userInfo = reactive({
  name: '张教授',
  role: '教务管理员',
  avatar: '/default-avatar.png'
})

// 初始化用户信息
const initUserInfo = () => {
  const currentUser = localStorage.getItem('currentUser')
  if (currentUser) {
    const user = JSON.parse(currentUser)
    userInfo.name = user.name
    // 正确映射用户角色
    if (user.role === '超级管理员') {
      userInfo.role = '系统管理员'
    } else if (user.role === '教务管理员') {
      userInfo.role = '教务管理员'
    } else if (user.role === '教师') {
      userInfo.role = '教师'
    } else if (user.role === '学生') {
      userInfo.role = '学生'
    } else {
      userInfo.role = user.role // 保持原值
    }
  }
}

// 页面加载时初始化用户信息
initUserInfo()

// 当前日期和欢迎信息
const currentDate = ref('')
const welcomeMessage = ref('')

// 统计数据
const stats = reactive({
  totalStudents: 15420,
  totalCourses: 1286,
  totalGrades: 45680
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'grade',
    icon: '📝',
    title: '高等数学A成绩已录入',
    time: '2小时前'
  },
  {
    id: 2,
    type: 'student',
    icon: '👨‍🎓',
    title: '新增学生信息 - 李明',
    time: '4小时前'
  },
  {
    id: 3,
    type: 'system',
    icon: '⚙️',
    title: '系统备份完成',
    time: '6小时前'
  },
  {
    id: 4,
    type: 'analytics',
    icon: '📊',
    title: '月度报表生成完成',
    time: '1天前'
  }
])

// 页面导航
const navigateTo = (path: string) => {
  router.push(path)
}

// 初始化
onMounted(() => {
  // 初始化用户信息
  initUserInfo()

  // 设置当前日期和欢迎信息
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[now.getDay()]

  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })

  welcomeMessage.value = `今天是 ${year}年${month}月${day}日${weekday}，祝您工作愉快！`
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.welcome-text h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.welcome-text p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 2rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 主要内容 */
.main-content {
  display: grid;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

/* 快速操作卡片 */
.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.action-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.action-card p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.card-arrow {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  color: #ccc;
  transition: all 0.3s ease;
}

.action-card:hover .card-arrow {
  color: #667eea;
  transform: translateX(5px);
}

/* 数据概览 */
.data-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.overview-left,
.overview-right {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.activity-icon.grade {
  background: #e3f2fd;
}

.activity-icon.student {
  background: #f3e5f5;
}

.activity-icon.system {
  background: #e8f5e8;
}

.activity-icon.analytics {
  background: #fff3e0;
}

.activity-title {
  font-weight: 500;
  color: #333;
}

.activity-time {
  font-size: 0.8rem;
  color: #999;
}

/* 通知公告 */
.notification-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.notification-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #ddd;
  transition: all 0.3s ease;
}

.notification-card.important {
  border-left-color: #e74c3c;
}

.notification-card.normal {
  border-left-color: #3498db;
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.notification-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.notification-card.important .notification-badge {
  background: #fee;
  color: #e74c3c;
}

.notification-card.normal .notification-badge {
  background: #e3f2fd;
  color: #3498db;
}

.notification-date {
  font-size: 0.8rem;
  color: #999;
}

.notification-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.notification-card p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-stats {
    justify-content: center;
  }
  
  .data-overview {
    grid-template-columns: 1fr;
  }
  
  .action-cards {
    grid-template-columns: 1fr;
  }
  
  .notification-cards {
    grid-template-columns: 1fr;
  }
}
</style>
