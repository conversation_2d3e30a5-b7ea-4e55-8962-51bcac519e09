<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/jou-logo.svg" alt="江苏海洋大学" class="logo-img">
          <span v-if="!sidebarCollapsed" class="logo-text">StudentMIS</span>
        </div>
        <button class="collapse-btn" @click="toggleSidebar">
          <span class="collapse-icon">{{ sidebarCollapsed ? '→' : '←' }}</span>
        </button>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-section">
          <div v-if="!sidebarCollapsed" class="nav-title">主要功能</div>
          <router-link
            v-for="item in mainMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ 'active': $route.path === item.path }"
          >
            <span class="nav-icon">
              <img v-if="item.icon.endsWith('.svg')" :src="item.icon" :alt="item.title" class="nav-icon-svg">
              <span v-else>{{ item.icon }}</span>
            </span>
            <span v-if="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
          </router-link>
        </div>
        
        <div class="nav-section">
          <div v-if="!sidebarCollapsed" class="nav-title">系统管理</div>
          <router-link
            v-for="item in systemMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ 'active': $route.path === item.path }"
          >
            <span class="nav-icon">
              <img v-if="item.icon.endsWith('.svg')" :src="item.icon" :alt="item.title" class="nav-icon-svg">
              <span v-else>{{ item.icon }}</span>
            </span>
            <span v-if="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
          </router-link>
        </div>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <h1 class="page-title">{{ pageTitle }}</h1>
          <div class="breadcrumb">
            <span v-for="(crumb, index) in breadcrumbs" :key="index">
              <router-link v-if="crumb.path" :to="crumb.path">{{ crumb.title }}</router-link>
              <span v-else>{{ crumb.title }}</span>
              <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">/</span>
            </span>
          </div>
        </div>
        
        <div class="header-right">
          <div class="header-actions">
            <button class="action-btn notification-btn" @click="handleNotificationClick">
              <span class="btn-icon">🔔</span>
              <span class="notification-badge">3</span>
            </button>
            <button class="action-btn search-btn" @click="handleSearchClick">
              <span class="btn-icon">🔍</span>
            </button>
          </div>
          
          <div class="user-menu" @click="toggleUserMenu">
            <div class="user-avatar">
              <img src="/default-avatar.svg" alt="用户头像">
            </div>
            <div class="user-info">
              <div class="user-name">{{ currentUser.name }}</div>
              <div class="user-role">{{ currentUser.role === 'admin' ? '系统管理员' : currentUser.role === 'teacher' ? '教师' : currentUser.role === 'student' ? '学生' : '教务管理员' }}</div>
            </div>
            <span class="dropdown-arrow">▼</span>
            
            <div v-if="userMenuVisible" class="user-dropdown">
              <a href="#" class="dropdown-item" @click.prevent="handleProfile">
                <span class="item-icon">👤</span>
                个人资料
              </a>
              <a href="#" class="dropdown-item" @click.prevent="handleSettings">
                <span class="item-icon">⚙️</span>
                账户设置
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click.prevent="logout">
                <span class="item-icon">🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="page-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 侧边栏状态
const sidebarCollapsed = ref(false)
const userMenuVisible = ref(false)

// 菜单项
const mainMenuItems = [
  { path: '/dashboard', title: '仪表板', icon: '/icons/dashboard.svg' },
  { path: '/students', title: '学生管理', icon: '/icons/students.svg' },
  { path: '/grades', title: '成绩管理', icon: '/icons/grades.svg' },
  { path: '/courses', title: '课程管理', icon: '/icons/courses.svg' },
  { path: '/analytics', title: '数据分析', icon: '/icons/analytics.svg' }
]

const systemMenuItems = [
  { path: '/users', title: '用户管理', icon: '/icons/users.svg' },
  { path: '/roles', title: '角色权限', icon: '/icons/roles.svg' },
  { path: '/settings', title: '系统设置', icon: '/icons/settings.svg' },
  { path: '/logs', title: '操作日志', icon: '/icons/logs.svg' }
]

// 获取当前用户信息
const currentUser = computed(() => {
  const userStr = localStorage.getItem('currentUser')
  if (userStr) {
    return JSON.parse(userStr)
  }
  return { name: '张教授', role: '教务管理员' }
})

// 页面标题和面包屑
const pageTitle = computed(() => {
  const currentItem = [...mainMenuItems, ...systemMenuItems].find(item => item.path === route.path)
  return currentItem?.title || '江苏海洋大学学生成绩管理系统'
})

const breadcrumbs = computed(() => {
  const crumbs = [{ title: '首页', path: '/dashboard' }]
  
  if (route.path !== '/dashboard') {
    const currentItem = [...mainMenuItems, ...systemMenuItems].find(item => item.path === route.path)
    if (currentItem) {
      crumbs.push({ title: currentItem.title, path: '' })
    }
  }
  
  return crumbs
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleUserMenu = () => {
  userMenuVisible.value = !userMenuVisible.value
}

const logout = () => {
  // 清除用户信息
  localStorage.removeItem('currentUser')
  // 处理退出登录
  router.push('/login')
}

// 通知点击处理
const handleNotificationClick = () => {
  alert('通知中心\n\n您有3条未读通知：\n1. 期末考试成绩录入通知\n2. 系统维护公告\n3. 新学期选课开始')
}

// 搜索点击处理
const handleSearchClick = () => {
  const keyword = prompt('请输入搜索关键词：')
  if (keyword) {
    alert(`搜索功能\n\n正在搜索："${keyword}"\n\n搜索结果将在新页面中显示`)
  }
}

// 个人资料处理
const handleProfile = () => {
  alert('个人资料\n\n功能开发中...\n将跳转到个人资料编辑页面')
}

// 账户设置处理
const handleSettings = () => {
  alert('账户设置\n\n功能开发中...\n将跳转到账户设置页面')
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  const userMenu = document.querySelector('.user-menu')
  if (userMenu && !userMenu.contains(event.target as Node)) {
    userMenuVisible.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e1e5e9;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  background: #f8f9fa;
}

.collapse-icon {
  font-size: 1rem;
  color: #666;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-title {
  padding: 0 1.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #333;
}

.nav-item.active {
  background: #e3f2fd;
  color: #1976d2;
  border-left-color: #1976d2;
}

.nav-icon {
  font-size: 1.25rem;
  width: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon-svg {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(42%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon-svg {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.nav-item:hover .nav-icon-svg {
  filter: brightness(0) saturate(100%) invert(20%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);
}

.nav-text {
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.breadcrumb {
  font-size: 0.9rem;
  color: #666;
}

.breadcrumb a {
  color: #1976d2;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: #ccc;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  position: relative;
  background: none;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f8f9fa;
}

.btn-icon {
  font-size: 1.25rem;
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: #e74c3c;
  color: white;
  font-size: 0.7rem;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* 用户菜单 */
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: #f8f9fa;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.8rem;
  color: #666;
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: #999;
  transition: transform 0.3s ease;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #333;
}

.item-icon {
  font-size: 1rem;
}

.dropdown-divider {
  height: 1px;
  background: #e1e5e9;
  margin: 0.5rem 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
  }
  
  .sidebar.collapsed {
    transform: translateX(0);
    width: 280px;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .top-header {
    padding: 1rem;
  }
  
  .header-left {
    flex: 1;
  }
  
  .page-title {
    font-size: 1.25rem;
  }
}
</style>
