<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="login-content">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="university-logo">
          <img src="/jou-logo.svg" alt="江苏海洋大学" class="logo-img">
        </div>
        <h1 class="system-title">江苏海洋大学学生成绩管理系统</h1>
        <h2 class="system-subtitle">StudentMIS V2.0</h2>
        <p class="system-description">
          现代化、智能化的学生成绩管理平台<br>
          为教育教学提供全方位数据支持
        </p>
        <div class="feature-list">
          <div class="feature-item">
            <i class="feature-icon">🎓</i>
            <span>智能成绩分析</span>
          </div>
          <div class="feature-item">
            <i class="feature-icon">📊</i>
            <span>数据可视化</span>
          </div>
          <div class="feature-item">
            <i class="feature-icon">🔒</i>
            <span>安全可靠</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form-card">
          <div class="form-header">
            <h3>系统登录</h3>
            <p>请输入您的账号信息</p>
          </div>
          
          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <label for="username">学号</label>
              <div class="input-wrapper">
                <i class="input-icon">👤</i>
                <input
                  id="username"
                  v-model="loginForm.username"
                  type="text"
                  placeholder="请输入学号"
                  class="form-input"
                  :class="{ 'error': errors.username }"
                  @blur="validateUsername"
                >
              </div>
              <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
            </div>
            
            <div class="form-group">
              <label for="password">密码</label>
              <div class="input-wrapper">
                <i class="input-icon">🔒</i>
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  class="form-input"
                  :class="{ 'error': errors.password }"
                  @blur="validatePassword"
                >
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  {{ showPassword ? '🙈' : '👁️' }}
                </button>
              </div>
              <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
            </div>
            
            <div class="form-group">
              <label class="remember-me">
                <input type="checkbox" v-model="loginForm.rememberMe">
                <span class="checkmark"></span>
                记住我
              </label>
            </div>
            
            <button
              type="submit"
              class="login-button"
              :class="{ 'loading': isLoading }"
              :disabled="isLoading"
            >
              <span v-if="!isLoading">登录</span>
              <span v-else class="loading-text">
                <i class="loading-spinner"></i>
                登录中...
              </span>
            </button>
          </form>
          
          <div class="form-footer">
            <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">忘记密码？</a>
            <span class="divider">|</span>
            <a href="#" class="contact-admin" @click.prevent="handleContactAdmin">联系管理员</a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部信息 -->
    <div class="login-footer">
      <p>&copy; 2024 江苏海洋大学. All rights reserved. | StudentMIS V2.0</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getAllStudents } from '../data/studentsData.js'
import { PermissionManager } from '@/utils/permissions'

const router = useRouter()
const permissionManager = PermissionManager.getInstance()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: ''
})

// 状态管理
const showPassword = ref(false)
const isLoading = ref(false)

// 表单验证
const validateUsername = () => {
  if (!loginForm.username) {
    errors.username = '请输入学号'
    return false
  }
  // 学号格式验证：10位数字，格式为年份(4位)+学院(2位)+专业(2位)+序号(2位)
  const studentIdPattern = /^\d{10}$/
  if (!studentIdPattern.test(loginForm.username)) {
    errors.username = '学号格式不正确，应为10位数字'
    return false
  }
  errors.username = ''
  return true
}

const validatePassword = () => {
  if (!loginForm.password) {
    errors.password = '请输入密码'
    return false
  }
  if (loginForm.password.length < 6) {
    errors.password = '密码至少6位'
    return false
  }
  errors.password = ''
  return true
}

// 登录处理
const handleLogin = async () => {
  // 验证表单
  const isUsernameValid = validateUsername()
  const isPasswordValid = validatePassword()

  if (!isUsernameValid || !isPasswordValid) {
    return
  }

  isLoading.value = true

  try {
    // 清理可能存在的旧缓存数据
    localStorage.removeItem('currentUser')
    localStorage.removeItem('systemUsers')

    // 模拟登录验证
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 验证密码必须与学号相同
    if (loginForm.password !== loginForm.username) {
      alert('学号或密码错误！\n\n提示：密码必须与学号相同')
      return
    }

    // 根据学号确定角色
    const userRole = PermissionManager.determineUserRole(loginForm.username)
    let userName = ''

    if (userRole === '超级管理员') {
      userName = '系统管理员'
    } else if (userRole === '教务管理员') {
      userName = `教务管理员${loginForm.username.slice(-2)}`
    } else if (userRole === '教师') {
      userName = `教师${loginForm.username.slice(-4)}`
    } else {
      // 学生：查找真实学生数据
      const students = getAllStudents()
      const student = students.find(s => s.studentId === loginForm.username)

      if (student) {
        userName = student.name
      } else {
        userName = `学生${loginForm.username.slice(-4)}`
      }
    }

    // 创建用户对象
    const user = {
      studentId: loginForm.username,
      name: userName,
      role: userRole
    }

    // 设置权限管理器的当前用户
    permissionManager.setCurrentUser(user)

    // 存储用户信息到localStorage
    localStorage.setItem('currentUser', JSON.stringify(user))

    // 登录成功提示
    alert(`登录成功！\n\n欢迎您，${userName}\n角色：${userRole}`)

    // 登录成功，跳转到仪表板
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请重试！')
  } finally {
    isLoading.value = false
  }
}

// 忘记密码处理
const handleForgotPassword = () => {
  alert('忘记密码功能\n\n请联系管理员重置密码：\n电话：0518-85895000\n邮箱：<EMAIL>')
}

// 联系管理员处理
const handleContactAdmin = () => {
  alert('联系管理员\n\n江苏海洋大学教务处\n电话：0518-85895000\n邮箱：<EMAIL>\n地址：江苏省连云港市海州区苍梧路59号')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容区域 */
.login-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 4rem;
}

/* 左侧信息区域 */
.login-info {
  flex: 1;
  max-width: 500px;
  color: white;
  text-align: center;
}

.university-logo {
  margin-bottom: 2rem;
}

.logo-img {
  width: 120px;
  height: 120px;
  object-fit: contain;
  /* 移除白色滤镜，显示原始彩色logo */
}

.system-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.system-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.feature-list {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* 右侧登录表单 */
.login-form-container {
  flex: 1;
  max-width: 400px;
}

.login-form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-header p {
  color: #666;
  font-size: 0.9rem;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  font-size: 1.2rem;
  color: #666;
  z-index: 1;
}

.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
  z-index: 1;
}

.error-message {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

/* 记住我复选框 */
.remember-me {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
}

.remember-me input {
  margin-right: 0.5rem;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单底部 */
.form-footer {
  text-align: center;
  font-size: 0.9rem;
}

.form-footer a {
  color: #667eea;
  text-decoration: none;
}

.form-footer a:hover {
  text-decoration: underline;
}

.divider {
  margin: 0 1rem;
  color: #ccc;
}

/* 底部信息 */
.login-footer {
  text-align: center;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .login-info {
    order: 2;
  }
  
  .login-form-container {
    order: 1;
    width: 100%;
  }
  
  .system-title {
    font-size: 2rem;
  }
  
  .feature-list {
    gap: 1rem;
  }
}
</style>
