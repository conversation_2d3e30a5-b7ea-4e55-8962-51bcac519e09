<template>
  <div class="analytics-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>数据分析</h1>
        <p>学生成绩数据统计分析与可视化展示</p>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary">
          <span class="btn-icon">📊</span>
          生成报告
        </button>
        <button class="btn btn-secondary">
          <span class="btn-icon">📤</span>
          导出分析
        </button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-selector">
      <div class="selector-group">
        <label>分析时间范围</label>
        <div class="time-buttons">
          <button
            v-for="period in timePeriods"
            :key="period.value"
            class="time-btn"
            :class="{ active: selectedPeriod === period.value }"
            @click="selectedPeriod = period.value"
          >
            {{ period.label }}
          </button>
        </div>
      </div>
      <div class="custom-range">
        <input type="date" v-model="customRange.start" class="date-input">
        <span>至</span>
        <input type="date" v-model="customRange.end" class="date-input">
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-section">
      <div class="metric-card">
        <div class="metric-icon">👥</div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.totalStudents }}</div>
          <div class="metric-label">总学生数</div>
          <div class="metric-change positive">+5.2%</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">📚</div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.totalCourses }}</div>
          <div class="metric-label">开设课程</div>
          <div class="metric-change positive">+2.1%</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">📈</div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.avgScore }}</div>
          <div class="metric-label">平均分</div>
          <div class="metric-change positive">+1.8%</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">🎯</div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.passRate }}%</div>
          <div class="metric-label">及格率</div>
          <div class="metric-change negative">-0.5%</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 成绩分布图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>成绩分布统计</h3>
          <div class="chart-controls">
            <select v-model="gradeDistribution.subject" class="chart-select">
              <option value="all">全部科目</option>
              <option value="math">高等数学</option>
              <option value="physics">大学物理</option>
              <option value="english">大学英语</option>
            </select>
          </div>
        </div>
        <div class="chart-container">
          <div class="grade-distribution-chart">
            <div class="chart-bars">
              <div class="bar-item">
                <div class="bar-container">
                  <div
                    class="bar excellent"
                    :style="{ height: gradeDistributionPercentage.excellent + '%' }"
                  ></div>
                </div>
                <div class="bar-label">优秀</div>
                <div class="bar-value">{{ gradeDistribution.excellent }}</div>
                <div class="bar-percentage">{{ gradeDistributionPercentage.excellent }}%</div>
              </div>
              <div class="bar-item">
                <div class="bar-container">
                  <div
                    class="bar good"
                    :style="{ height: gradeDistributionPercentage.good + '%' }"
                  ></div>
                </div>
                <div class="bar-label">良好</div>
                <div class="bar-value">{{ gradeDistribution.good }}</div>
                <div class="bar-percentage">{{ gradeDistributionPercentage.good }}%</div>
              </div>
              <div class="bar-item">
                <div class="bar-container">
                  <div
                    class="bar average"
                    :style="{ height: gradeDistributionPercentage.average + '%' }"
                  ></div>
                </div>
                <div class="bar-label">中等</div>
                <div class="bar-value">{{ gradeDistribution.average }}</div>
                <div class="bar-percentage">{{ gradeDistributionPercentage.average }}%</div>
              </div>
              <div class="bar-item">
                <div class="bar-container">
                  <div
                    class="bar pass"
                    :style="{ height: gradeDistributionPercentage.pass + '%' }"
                  ></div>
                </div>
                <div class="bar-label">及格</div>
                <div class="bar-value">{{ gradeDistribution.pass }}</div>
                <div class="bar-percentage">{{ gradeDistributionPercentage.pass }}%</div>
              </div>
              <div class="bar-item">
                <div class="bar-container">
                  <div
                    class="bar fail"
                    :style="{ height: gradeDistributionPercentage.fail + '%' }"
                  ></div>
                </div>
                <div class="bar-label">不及格</div>
                <div class="bar-value">{{ gradeDistribution.fail }}</div>
                <div class="bar-percentage">{{ gradeDistributionPercentage.fail }}%</div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color excellent"></span>
                <span>优秀 (90-100分)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color good"></span>
                <span>良好 (80-89分)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color average"></span>
                <span>中等 (70-79分)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color pass"></span>
                <span>及格 (60-69分)</span>
              </div>
              <div class="legend-item">
                <span class="legend-color fail"></span>
                <span>不及格 (0-59分)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势分析图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>成绩趋势分析</h3>
          <div class="chart-controls">
            <select v-model="trendAnalysis.type" class="chart-select">
              <option value="semester">按学期</option>
              <option value="month">按月份</option>
              <option value="week">按周</option>
            </select>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-placeholder">
            <div class="chart-icon">📈</div>
            <p>成绩趋势折线图</p>
            <small>显示平均分变化趋势</small>
          </div>
        </div>
      </div>
    </div>

    <!-- 专业对比分析 -->
    <div class="comparison-section">
      <div class="comparison-card">
        <div class="card-header">
          <h3>专业成绩对比</h3>
          <button class="btn btn-outline">查看详情</button>
        </div>
        <div class="comparison-grid">
          <div
            v-for="major in majorComparison"
            :key="major.name"
            class="major-item"
          >
            <div class="major-info">
              <div class="major-name">{{ major.name }}</div>
              <div class="major-students">{{ major.students }}人</div>
            </div>
            <div class="major-score">
              <div class="score-value">{{ major.avgScore }}</div>
              <div class="score-bar">
                <div
                  class="score-fill"
                  :style="{ width: (major.avgScore / 100) * 100 + '%' }"
                ></div>
              </div>
            </div>
            <div class="major-rank">
              <span class="rank-badge" :class="getRankClass(major.rank)">
                第{{ major.rank }}名
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程难度分析 -->
      <div class="difficulty-card">
        <div class="card-header">
          <h3>课程难度分析</h3>
          <button class="btn btn-outline">详细报告</button>
        </div>
        <div class="difficulty-list">
          <div
            v-for="course in courseDifficulty"
            :key="course.name"
            class="difficulty-item"
          >
            <div class="course-info">
              <div class="course-name">{{ course.name }}</div>
              <div class="course-code">{{ course.code }}</div>
            </div>
            <div class="difficulty-level">
              <div class="level-indicator" :class="course.difficulty">
                {{ getDifficultyText(course.difficulty) }}
              </div>
              <div class="level-score">平均分: {{ course.avgScore }}</div>
            </div>
            <div class="pass-rate">
              <div class="rate-value">{{ course.passRate }}%</div>
              <div class="rate-label">及格率</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习建议 -->
    <div class="suggestions-section">
      <div class="suggestions-card">
        <div class="card-header">
          <h3>智能分析建议</h3>
          <span class="ai-badge">AI分析</span>
        </div>
        <div class="suggestions-list">
          <div
            v-for="suggestion in suggestions"
            :key="suggestion.id"
            class="suggestion-item"
            :class="suggestion.type"
          >
            <div class="suggestion-icon">{{ suggestion.icon }}</div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
            </div>
            <div class="suggestion-priority">{{ suggestion.priority }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { getAllStudents } from '@/data/studentsData.js'

// 数据类型定义
interface TimePeriod {
  label: string
  value: string
}

interface Major {
  name: string
  students: number
  avgScore: number
  rank: number
}

interface Course {
  name: string
  code: string
  difficulty: 'easy' | 'medium' | 'hard'
  avgScore: number
  passRate: number
}

interface Suggestion {
  id: number
  type: 'warning' | 'info' | 'success'
  icon: string
  title: string
  description: string
  priority: string
}

// 响应式数据
const selectedPeriod = ref('semester')
const customRange = reactive({
  start: '',
  end: ''
})

const gradeDistribution = reactive({
  subject: 'all'
})

const trendAnalysis = reactive({
  type: 'semester'
})

// 时间周期选项
const timePeriods: TimePeriod[] = [
  { label: '本学期', value: 'semester' },
  { label: '本学年', value: 'year' },
  { label: '近3个月', value: '3months' },
  { label: '近6个月', value: '6months' },
  { label: '自定义', value: 'custom' }
]

// 获取真实数据
const getRealAnalyticsData = () => {
  const studentsData = getAllStudents().slice(0, 1000) // 限制数量提高性能

  const coursesData = [
    { id: 1, code: 'CS101', name: '计算机科学导论', credits: 3 },
    { id: 2, code: 'MATH101', name: '高等数学A', credits: 5 },
    { id: 3, code: 'ENG101', name: '大学英语', credits: 2 },
    { id: 4, code: 'CS201', name: '数据结构与算法', credits: 4 }
  ]

  // 生成成绩数据
  const gradesData = []
  studentsData.forEach(student => {
    const courseCount = Math.floor(Math.random() * 2) + 2 // 2-3门课程
    const selectedCourses = coursesData.slice(0, courseCount)

    selectedCourses.forEach(course => {
      const baseScore = 70 + Math.random() * 25
      const variation = (Math.random() - 0.5) * 10

      const regularScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const midtermScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const finalScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

      gradesData.push({
        studentId: student.studentId,
        studentName: student.name,
        courseCode: course.code,
        courseName: course.name,
        totalScore,
        credits: course.credits
      })
    })
  })

  return { students: studentsData, courses: coursesData, grades: gradesData }
}

const realData = getRealAnalyticsData()

// 计算真实指标
const calculateMetrics = () => {
  const totalGrades = realData.grades.length
  const totalScore = realData.grades.reduce((sum, grade) => sum + grade.totalScore, 0)
  const passCount = realData.grades.filter(grade => grade.totalScore >= 60).length

  return {
    totalStudents: realData.students.length,
    totalCourses: realData.courses.length,
    avgScore: totalGrades > 0 ? (totalScore / totalGrades).toFixed(1) : 0,
    passRate: totalGrades > 0 ? ((passCount / totalGrades) * 100).toFixed(1) : 0
  }
}

// 关键指标
const metrics = reactive(calculateMetrics())

// 成绩分布数据
const gradeDistribution = computed(() => {
  const distribution = {
    excellent: 0,  // 90-100
    good: 0,       // 80-89
    average: 0,    // 70-79
    pass: 0,       // 60-69
    fail: 0        // 0-59
  }

  realData.grades.forEach(grade => {
    const score = grade.totalScore
    if (score >= 90) distribution.excellent++
    else if (score >= 80) distribution.good++
    else if (score >= 70) distribution.average++
    else if (score >= 60) distribution.pass++
    else distribution.fail++
  })

  return distribution
})

// 成绩分布百分比
const gradeDistributionPercentage = computed(() => {
  const total = realData.grades.length
  if (total === 0) return { excellent: 0, good: 0, average: 0, pass: 0, fail: 0 }

  return {
    excellent: Math.round((gradeDistribution.value.excellent / total) * 100),
    good: Math.round((gradeDistribution.value.good / total) * 100),
    average: Math.round((gradeDistribution.value.average / total) * 100),
    pass: Math.round((gradeDistribution.value.pass / total) * 100),
    fail: Math.round((gradeDistribution.value.fail / total) * 100)
  }
})

// 专业对比数据
const majorComparison: Major[] = [
  { name: '计算机科学与技术', students: 3240, avgScore: 85.2, rank: 1 },
  { name: '软件工程', students: 2890, avgScore: 84.1, rank: 2 },
  { name: '数据科学与大数据技术', students: 2156, avgScore: 83.7, rank: 3 },
  { name: '人工智能', students: 1980, avgScore: 82.9, rank: 4 },
  { name: '网络工程', students: 1654, avgScore: 81.5, rank: 5 }
]

// 课程难度分析
const courseDifficulty: Course[] = [
  { name: '高等数学A', code: 'MATH101', difficulty: 'hard', avgScore: 76.8, passRate: 89.2 },
  { name: '线性代数', code: 'MATH102', difficulty: 'medium', avgScore: 82.1, passRate: 94.5 },
  { name: '数据结构', code: 'CS201', difficulty: 'hard', avgScore: 78.9, passRate: 91.3 },
  { name: '计算机网络', code: 'CS301', difficulty: 'medium', avgScore: 84.2, passRate: 96.1 },
  { name: '大学英语', code: 'ENG101', difficulty: 'easy', avgScore: 87.5, passRate: 98.7 }
]

// 智能建议
const suggestions: Suggestion[] = [
  {
    id: 1,
    type: 'warning',
    icon: '⚠️',
    title: '高等数学A课程需要关注',
    description: '该课程平均分较低，建议加强辅导和答疑',
    priority: '高'
  },
  {
    id: 2,
    type: 'info',
    icon: 'ℹ️',
    title: '计算机专业表现优异',
    description: '计算机相关专业成绩持续领先，可作为标杆',
    priority: '中'
  },
  {
    id: 3,
    type: 'success',
    icon: '✅',
    title: '整体及格率稳定',
    description: '本学期及格率保持在94%以上，教学质量良好',
    priority: '低'
  }
]

// 方法
const getRankClass = (rank: number): string => {
  if (rank <= 2) return 'top'
  if (rank <= 4) return 'middle'
  return 'bottom'
}

const getDifficultyText = (difficulty: string): string => {
  const map = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return map[difficulty as keyof typeof map] || '未知'
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.analytics-container {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.header-content p {
  color: #666;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-outline {
  background: white;
  color: #667eea;
  border: 1px solid #667eea;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

/* 时间选择器 */
.time-selector {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.selector-group label {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}

.time-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.time-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.time-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input {
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

/* 关键指标 */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.metric-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.metric-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.metric-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.metric-change.positive {
  color: #2e7d32;
}

.metric-change.negative {
  color: #d32f2f;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.chart-select {
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background: white;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.excellent { background: #4caf50; }
.legend-color.good { background: #2196f3; }
.legend-color.average { background: #ff9800; }
.legend-color.pass { background: #9c27b0; }
.legend-color.fail { background: #f44336; }

/* 对比分析 */
.comparison-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.comparison-card,
.difficulty-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.card-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.comparison-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.major-item {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.major-item:hover {
  background: #f8f9fa;
}

.major-name {
  font-weight: 500;
  color: #333;
}

.major-students {
  font-size: 0.8rem;
  color: #666;
}

.major-score {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.score-value {
  font-weight: 600;
  color: #333;
}

.score-bar {
  height: 6px;
  background: #e1e5e9;
  border-radius: 3px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.rank-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.rank-badge.top {
  background: #e8f5e8;
  color: #2e7d32;
}

.rank-badge.middle {
  background: #e3f2fd;
  color: #1976d2;
}

.rank-badge.bottom {
  background: #fff3e0;
  color: #f57c00;
}

/* 课程难度 */
.difficulty-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.difficulty-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 10px;
}

.course-name {
  font-weight: 500;
  color: #333;
}

.course-code {
  font-size: 0.8rem;
  color: #666;
}

.level-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.level-indicator.easy {
  background: #e8f5e8;
  color: #2e7d32;
}

.level-indicator.medium {
  background: #fff3e0;
  color: #f57c00;
}

.level-indicator.hard {
  background: #ffebee;
  color: #d32f2f;
}

.level-score {
  font-size: 0.8rem;
  color: #666;
}

.rate-value {
  font-weight: 600;
  color: #333;
}

.rate-label {
  font-size: 0.8rem;
  color: #666;
}

/* 智能建议 */
.suggestions-section {
  margin-bottom: 2rem;
}

.suggestions-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.ai-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #ddd;
}

.suggestion-item.warning {
  background: #fff8e1;
  border-left-color: #ff9800;
}

.suggestion-item.info {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.suggestion-item.success {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.suggestion-icon {
  font-size: 1.5rem;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

.suggestion-desc {
  color: #666;
  font-size: 0.9rem;
}

.suggestion-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background: #f5f5f5;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analytics-container {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .time-selector {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .metrics-section {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .comparison-section {
    grid-template-columns: 1fr;
  }
  
  .major-item {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* 成绩分布图表样式 */
.grade-distribution-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 1rem 0;
  border-bottom: 2px solid #e1e5e9;
  position: relative;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  max-width: 80px;
}

.bar-container {
  height: 150px;
  width: 40px;
  display: flex;
  align-items: end;
  position: relative;
}

.bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.8s ease;
  animation: growUp 1.5s ease-out;
  position: relative;
}

@keyframes growUp {
  from {
    height: 0 !important;
  }
  to {
    height: var(--final-height);
  }
}

.bar.excellent {
  background: linear-gradient(to top, #2e7d32, #4caf50);
}

.bar.good {
  background: linear-gradient(to top, #1976d2, #2196f3);
}

.bar.average {
  background: linear-gradient(to top, #f57c00, #ff9800);
}

.bar.pass {
  background: linear-gradient(to top, #c2185b, #e91e63);
}

.bar.fail {
  background: linear-gradient(to top, #d32f2f, #f44336);
}

.bar-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.bar-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1976d2;
}

.bar-percentage {
  font-size: 0.8rem;
  color: #666;
}
</style>
