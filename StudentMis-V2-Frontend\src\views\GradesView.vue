<template>
  <div class="grades-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>成绩管理</h1>
        <p>录入、查询和管理学生成绩信息</p>
      </div>
      <div class="header-actions">
        <button v-if="canInputGrades" class="btn btn-primary" @click="showGradeDialog = true">
          <span class="btn-icon">➕</span>
          录入成绩
        </button>
        <button v-if="checkPermission('report.view')" class="btn btn-secondary">
          <span class="btn-icon">📊</span>
          成绩统计
        </button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-group">
          <label>学期</label>
          <select v-model="filters.semester" class="filter-select">
            <option value="">全部学期</option>
            <option value="2024-1">2024年春季学期</option>
            <option value="2023-2">2023年秋季学期</option>
            <option value="2023-1">2023年春季学期</option>
          </select>
        </div>
        <div class="filter-group">
          <label>课程</label>
          <select v-model="filters.course" class="filter-select">
            <option value="">全部课程</option>
            <option value="高等数学A">高等数学A</option>
            <option value="线性代数">线性代数</option>
            <option value="数据结构">数据结构</option>
            <option value="计算机网络">计算机网络</option>
          </select>
        </div>
        <div class="filter-group">
          <label>年级</label>
          <select v-model="filters.grade" class="filter-select">
            <option value="">全部年级</option>
            <option value="2024">2024级</option>
            <option value="2023">2023级</option>
            <option value="2022">2022级</option>
            <option value="2021">2021级</option>
          </select>
        </div>
        <div class="filter-group">
          <label>成绩范围</label>
          <select v-model="filters.scoreRange" class="filter-select">
            <option value="">全部成绩</option>
            <option value="excellent">优秀 (90-100)</option>
            <option value="good">良好 (80-89)</option>
            <option value="average">中等 (70-79)</option>
            <option value="pass">及格 (60-69)</option>
            <option value="fail">不及格 (<60)</option>
          </select>
        </div>
      </div>
      <div class="search-row">
        <div class="search-input-group">
          <span class="search-icon">🔍</span>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索学生姓名或学号..."
            class="search-input"
          >
        </div>
        <button class="btn btn-outline" @click="resetFilters">
          重置筛选
        </button>
      </div>
    </div>

    <!-- 成绩统计和进度分析 -->
    <div class="analytics-section">
      <!-- 成绩统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon excellent">📈</div>
          <div class="stat-content">
            <div class="stat-number">{{ gradeStats.excellent }}</div>
            <div class="stat-label">优秀</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon good">📊</div>
          <div class="stat-content">
            <div class="stat-number">{{ gradeStats.good }}</div>
            <div class="stat-label">良好</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon average">📉</div>
          <div class="stat-content">
            <div class="stat-number">{{ gradeStats.average }}</div>
            <div class="stat-label">中等</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon pass">✅</div>
          <div class="stat-content">
            <div class="stat-number">{{ gradeStats.pass }}</div>
            <div class="stat-label">及格</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon fail">❌</div>
          <div class="stat-content">
            <div class="stat-number">{{ gradeStats.fail }}</div>
            <div class="stat-label">不及格</div>
          </div>
        </div>
      </div>

      <!-- 进度饼图区域 -->
      <div class="progress-charts">
        <!-- 学分进度饼图 -->
        <div class="chart-card">
          <h3>学分完成进度</h3>
          <div class="pie-chart-container">
            <div class="pie-chart" :style="creditProgressStyle">
              <div class="pie-chart-center">
                <div class="progress-value">{{ creditProgress.percentage }}%</div>
                <div class="progress-label">已完成</div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color completed"></span>
                <span>已获得学分: {{ creditProgress.completed }}</span>
              </div>
              <div class="legend-item">
                <span class="legend-color remaining"></span>
                <span>剩余学分: {{ creditProgress.remaining }}</span>
              </div>
              <div class="legend-total">
                总计: {{ creditProgress.total }} 学分
              </div>
            </div>
          </div>
        </div>

        <!-- 毕业进度饼图 -->
        <div class="chart-card">
          <h3>毕业要求进度</h3>
          <div class="pie-chart-container">
            <div class="pie-chart" :style="graduationProgressStyle">
              <div class="pie-chart-center">
                <div class="progress-value">{{ graduationProgress.percentage }}%</div>
                <div class="progress-label">完成度</div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color excellent"></span>
                <span>学分进度: {{ graduationProgress.details?.creditProgress || 0 }}%</span>
              </div>
              <div class="legend-item">
                <span class="legend-color good"></span>
                <span>GPA达标: {{ graduationProgress.details?.gpaProgress || 0 }}%</span>
              </div>
              <div class="legend-item">
                <span class="legend-color average"></span>
                <span>课程完成: {{ graduationProgress.details?.courseProgress || 0 }}%</span>
              </div>
              <div class="legend-item">
                <span class="legend-color warning"></span>
                <span>挂科控制: {{ graduationProgress.details?.failProgress || 0 }}%</span>
              </div>
              <div class="legend-total">
                已获得学分: {{ graduationProgress.details?.completedCredits || 0 }} / 160
              </div>
              <div class="legend-total">
                当前GPA: {{ graduationProgress.details?.currentGPA || '0.00' }} / 4.00
              </div>
            </div>
          </div>
        </div>

        <!-- 平均绩点显示 -->
        <div class="chart-card gpa-card">
          <h3>平均绩点</h3>
          <div class="gpa-display-large">
            <div class="gpa-value" :class="getGPALevel(averageGPA)">
              {{ averageGPA.toFixed(2) }}
            </div>
            <div class="gpa-level-text">{{ getGPALevelText(averageGPA) }}</div>
            <div class="gpa-progress-bar">
              <div class="gpa-progress-fill" :style="{ width: (averageGPA / 4 * 100) + '%' }"></div>
            </div>
            <div class="gpa-scale">
              <span>0.0</span>
              <span>2.0</span>
              <span>3.0</span>
              <span>4.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成绩列表 -->
    <div class="grades-list">
      <div class="list-header">
        <h2>成绩记录</h2>
        <div class="list-actions">
          <button v-if="canExportData" class="action-btn" @click="exportGrades">
            <span class="btn-icon">📤</span>
            导出Excel
          </button>
          <button v-if="canManageGrades" class="action-btn" @click="batchImport">
            <span class="btn-icon">📥</span>
            批量导入
          </button>
          <button v-if="canInputGrades" class="action-btn" @click="batchGradeEntry">
            <span class="btn-icon">✏️</span>
            批量录入
          </button>
          <button v-if="checkPermission('report.view')" class="action-btn" @click="generateReports">
            <span class="btn-icon">📊</span>
            生成报表
          </button>
        </div>
      </div>

      <div class="table-container">
        <table class="grades-table">
          <thead>
            <tr>
              <th>学号</th>
              <th>姓名</th>
              <th>课程</th>
              <th>学期</th>
              <th>平时成绩</th>
              <th>期中成绩</th>
              <th>期末成绩</th>
              <th>总评成绩</th>
              <th>等级</th>
              <th>绩点</th>
              <th>学分</th>
              <th>录入时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="grade in paginatedGrades" :key="grade.id">
              <td>{{ grade.studentId }}</td>
              <td class="student-name">{{ grade.studentName }}</td>
              <td>{{ grade.courseName }}</td>
              <td>{{ grade.semester }}</td>
              <td>{{ grade.regularScore || '-' }}</td>
              <td>{{ grade.midtermScore || '-' }}</td>
              <td>{{ grade.finalScore || '-' }}</td>
              <td class="total-score">
                <span class="score-badge" :class="getScoreLevel(grade.totalScore)">
                  {{ grade.totalScore }}
                </span>
              </td>
              <td>
                <span class="grade-level" :class="getScoreLevel(grade.totalScore)">
                  {{ getGradeLevel(grade.totalScore) }}
                </span>
              </td>
              <td>
                <span class="gpa-badge" :class="getScoreLevel(grade.totalScore)">
                  {{ getGPA(grade.totalScore) }}
                </span>
              </td>
              <td>{{ grade.credits }}</td>
              <td class="input-time">{{ formatDate(grade.inputTime) }}</td>
              <td>
                <div class="table-actions">
                  <button v-if="canInputGrades" class="action-btn edit" @click="editGrade(grade)">
                    ✏️
                  </button>
                  <button v-if="canManageGrades" class="action-btn delete" @click="deleteGrade(grade)">
                    🗑️
                  </button>
                  <span v-if="!canInputGrades && !canManageGrades" class="no-permission">
                    无权限
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          class="page-btn"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页，共 {{ filteredGrades.length }} 条记录
        </span>
        <button
          class="page-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 编辑成绩对话框 -->
    <div v-if="showEditDialog" class="modal-overlay" @click="closeEditDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑成绩</h3>
          <button class="close-btn" @click="closeEditDialog">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveGrade">
            <div class="form-row">
              <div class="form-group">
                <label>学号</label>
                <input type="text" v-model="editingGrade.studentId" readonly class="form-input readonly">
              </div>
              <div class="form-group">
                <label>姓名</label>
                <input type="text" v-model="editingGrade.studentName" readonly class="form-input readonly">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>课程</label>
                <input type="text" v-model="editingGrade.courseName" readonly class="form-input readonly">
              </div>
              <div class="form-group">
                <label>学期</label>
                <input type="text" v-model="editingGrade.semester" readonly class="form-input readonly">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>平时成绩</label>
                <input
                  type="number"
                  v-model.number="editingGrade.regularScore"
                  min="0"
                  max="100"
                  class="form-input"
                  placeholder="请输入平时成绩"
                >
              </div>
              <div class="form-group">
                <label>期中成绩</label>
                <input
                  type="number"
                  v-model.number="editingGrade.midtermScore"
                  min="0"
                  max="100"
                  class="form-input"
                  placeholder="请输入期中成绩"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>期末成绩</label>
                <input
                  type="number"
                  v-model.number="editingGrade.finalScore"
                  min="0"
                  max="100"
                  class="form-input"
                  placeholder="请输入期末成绩"
                >
              </div>
              <div class="form-group">
                <label>学分</label>
                <input
                  type="number"
                  v-model.number="editingGrade.credits"
                  min="0"
                  max="10"
                  step="0.5"
                  class="form-input"
                  placeholder="请输入学分"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group full-width">
                <label>总评成绩</label>
                <input
                  type="text"
                  :value="calculateTotalScore()"
                  readonly
                  class="form-input readonly total-score-display"
                >
                <small class="form-hint">总评成绩 = 平时成绩 × 30% + 期中成绩 × 30% + 期末成绩 × 40%</small>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeEditDialog">取消</button>
          <button type="button" class="btn btn-primary" @click="saveGrade">保存</button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteDialog" class="modal-overlay" @click="closeDeleteDialog">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="closeDeleteDialog">×</button>
        </div>
        <div class="modal-body">
          <p>确定要删除 <strong>{{ deletingGrade?.studentName }}</strong> 的 <strong>{{ deletingGrade?.courseName }}</strong> 成绩吗？</p>
          <p class="warning-text">此操作不可撤销！</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeDeleteDialog">取消</button>
          <button type="button" class="btn btn-danger" @click="confirmDelete">确认删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { getAllStudents } from '../data/studentsData.js'
import { PermissionManager, checkPermission } from '@/utils/permissions'

// 数据类型定义
interface Grade {
  id: number
  studentId: string
  studentName: string
  courseName: string
  semester: string
  regularScore?: number
  midtermScore?: number
  finalScore?: number
  totalScore: number
  credits: number
  inputTime: Date
  grade: string
}

// 响应式数据
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = 15
const showGradeDialog = ref(false)

// 编辑对话框相关
const showEditDialog = ref(false)
const editingGrade = ref<Grade>({} as Grade)

// 删除对话框相关
const showDeleteDialog = ref(false)
const deletingGrade = ref<Grade | null>(null)

const filters = reactive({
  semester: '',
  course: '',
  grade: '',
  scoreRange: ''
})

// 权限管理器实例
const permissionManager = PermissionManager.getInstance()

// 获取当前用户信息
const currentUser = computed(() => {
  const userStr = localStorage.getItem('currentUser')
  if (userStr) {
    const user = JSON.parse(userStr)
    // 确保权限管理器有当前用户信息
    if (!permissionManager.getCurrentUser()) {
      permissionManager.setCurrentUser(user)
    }
    return user
  }
  return null
})

// 权限检查
const isAdmin = computed(() => {
  return checkPermission('user.manage') || checkPermission('system.config')
})

const canManageGrades = computed(() => {
  return checkPermission('grade.manage')
})

const canInputGrades = computed(() => {
  return checkPermission('grade.input')
})

const canViewAllGrades = computed(() => {
  return checkPermission('grade.manage') || checkPermission('student.manage')
})

const canExportData = computed(() => {
  return checkPermission('data.export')
})

// 成绩数据 - 基于5000名学生生成
const grades = ref<Grade[]>([])

// 生成5000名学生的成绩数据
const generateGradesData = () => {
  const students = getAllStudents()
  const courses = [
    { name: '高等数学A', credits: 4, semester: '2024-1' },
    { name: '线性代数', credits: 3, semester: '2024-1' },
    { name: '数据结构', credits: 4, semester: '2024-1' },
    { name: '计算机网络', credits: 3, semester: '2024-1' },
    { name: '数据库原理', credits: 3, semester: '2024-1' },
    { name: '操作系统', credits: 4, semester: '2023-2' },
    { name: '软件工程', credits: 3, semester: '2023-2' },
    { name: '编译原理', credits: 3, semester: '2023-2' },
    { name: '计算机组成原理', credits: 4, semester: '2023-2' },
    { name: '大学英语', credits: 2, semester: '2024-1' },
    { name: '大学物理', credits: 3, semester: '2023-2' },
    { name: '概率论与数理统计', credits: 3, semester: '2024-1' }
  ]

  const gradesData = []
  let gradeId = 1

  // 为每个学生生成多门课程的成绩
  students.forEach(student => {
    // 根据学生年级决定选课数量
    const gradeLevel = 2024 - student.grade + 1
    const courseCount = Math.min(courses.length, gradeLevel * 3 + Math.floor(Math.random() * 3))

    // 随机选择课程
    const selectedCourses = courses.slice(0, courseCount)

    selectedCourses.forEach(course => {
      // 生成成绩（大部分学生成绩在60-95之间）
      const baseScore = 60 + Math.random() * 35
      const variation = (Math.random() - 0.5) * 20

      const regularScore = Math.max(0, Math.min(100, Math.round(baseScore + variation)))
      const midtermScore = Math.max(0, Math.min(100, Math.round(baseScore + variation)))
      const finalScore = Math.max(0, Math.min(100, Math.round(baseScore + variation)))

      // 计算总评成绩
      const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

      const grade = {
        id: gradeId++,
        studentId: student.studentId,
        studentName: student.name,
        courseName: course.name,
        semester: course.semester,
        regularScore: regularScore,
        midtermScore: midtermScore,
        finalScore: finalScore,
        totalScore: totalScore,
        credits: course.credits,
        inputTime: new Date(2024, Math.floor(Math.random() * 6), Math.floor(Math.random() * 28) + 1),
        grade: getGradeLevel(totalScore)
      }

      gradesData.push(grade)
    })
  })

  return gradesData
}

// 计算属性
const filteredGrades = computed(() => {
  let result = grades.value

  // 权限过滤：学生只能看到自己的成绩
  if (!canViewAllGrades.value && currentUser.value) {
    result = result.filter(grade => grade.studentId === currentUser.value.studentId)
  }

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(grade =>
      grade.studentName.toLowerCase().includes(query) ||
      grade.studentId.includes(query)
    )
  }

  // 学期过滤
  if (filters.semester) {
    result = result.filter(grade => grade.semester === filters.semester)
  }

  // 课程过滤
  if (filters.course) {
    result = result.filter(grade => grade.courseName === filters.course)
  }

  // 年级过滤
  if (filters.grade) {
    result = result.filter(grade => grade.studentId.startsWith(filters.grade))
  }

  // 成绩范围过滤
  if (filters.scoreRange) {
    result = result.filter(grade => {
      const score = grade.totalScore
      switch (filters.scoreRange) {
        case 'excellent': return score >= 90
        case 'good': return score >= 80 && score < 90
        case 'average': return score >= 70 && score < 80
        case 'pass': return score >= 60 && score < 70
        case 'fail': return score < 60
        default: return true
      }
    })
  }

  return result
})

const paginatedGrades = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredGrades.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredGrades.value.length / pageSize)
})

const gradeStats = computed(() => {
  const stats = {
    excellent: 0,
    good: 0,
    average: 0,
    pass: 0,
    fail: 0
  }

  filteredGrades.value.forEach(grade => {
    const score = grade.totalScore
    if (score >= 90) stats.excellent++
    else if (score >= 80) stats.good++
    else if (score >= 70) stats.average++
    else if (score >= 60) stats.pass++
    else stats.fail++
  })

  return stats
})

// 计算平均绩点
const averageGPA = computed(() => {
  if (filteredGrades.value.length === 0) return 0

  let totalGPA = 0
  let totalCredits = 0

  filteredGrades.value.forEach(grade => {
    const gpa = getGPA(grade.totalScore)
    totalGPA += gpa * grade.credits
    totalCredits += grade.credits
  })

  return totalCredits > 0 ? totalGPA / totalCredits : 0
})

// 学分进度计算 - 基于当前用户
const creditProgress = computed(() => {
  const totalCredits = 160 // 毕业要求总学分

  // 获取当前用户的成绩
  const currentUserGrades = grades.value.filter(grade => {
    if (isAdmin.value) {
      // 管理员查看所有学生的平均学分进度
      return true
    } else {
      // 学生只看自己的成绩
      const user = currentUser.value
      if (!user || !user.studentId) {
        console.log('当前用户信息不完整:', user)
        return false
      }
      return grade.studentId === user.studentId
    }
  })

  const completedCredits = currentUserGrades.reduce((sum, grade) => {
    return grade.totalScore >= 60 ? sum + grade.credits : sum
  }, 0)

  const remaining = Math.max(0, totalCredits - completedCredits)
  let percentage = 0

  if (isAdmin.value) {
    // 管理员看到的是所有学生的平均完成度
    const allStudents = getAllStudents()
    let totalStudentCredits = 0

    allStudents.forEach(student => {
      const studentGrades = grades.value.filter(g => g.studentId === student.studentId)
      const studentCompletedCredits = studentGrades.reduce((sum, grade) => {
        return grade.totalScore >= 60 ? sum + grade.credits : sum
      }, 0)
      totalStudentCredits += studentCompletedCredits
    })

    const avgCreditsPerStudent = allStudents.length > 0 ? totalStudentCredits / allStudents.length : 0
    percentage = Math.round((avgCreditsPerStudent / totalCredits) * 100)

    return {
      completed: Math.round(avgCreditsPerStudent),
      remaining: Math.max(0, totalCredits - Math.round(avgCreditsPerStudent)),
      total: totalCredits,
      percentage: Math.min(100, percentage) // 确保不超过100%
    }
  } else {
    // 学生看到自己的进度
    percentage = Math.round((completedCredits / totalCredits) * 100)

    return {
      completed: completedCredits,
      remaining: remaining,
      total: totalCredits,
      percentage: Math.min(100, percentage) // 确保不超过100%
    }
  }
})

// 毕业进度计算 - 基于实际毕业要求
const graduationProgress = computed(() => {
  const stats = gradeStats.value

  // 获取当前用户的成绩数据
  let userGrades = filteredGrades.value
  if (!canViewAllGrades.value && currentUser.value) {
    userGrades = grades.value.filter(grade => grade.studentId === currentUser.value.studentId)
  }

  if (userGrades.length === 0) {
    return {
      excellent: 0,
      good: 0,
      average: 0,
      needImprovement: 0,
      percentage: 0
    }
  }

  // 毕业要求标准
  const graduationRequirements = {
    totalCredits: 160,        // 总学分要求
    minGPA: 2.0,             // 最低GPA要求
    requiredCourses: 40,     // 必修课程数量
    maxFailedCourses: 3      // 最大允许挂科门数
  }

  // 计算实际完成情况
  const completedCredits = userGrades.reduce((sum, grade) => {
    return grade.totalScore >= 60 ? sum + grade.credits : sum
  }, 0)

  const failedCourses = userGrades.filter(grade => grade.totalScore < 60).length
  const currentGPA = averageGPA.value

  // 计算各项完成度
  const creditProgress = Math.min(100, (completedCredits / graduationRequirements.totalCredits) * 100)
  const gpaProgress = Math.min(100, (currentGPA / 4.0) * 100) // 基于4.0满分
  const courseProgress = Math.min(100, (userGrades.length / graduationRequirements.requiredCourses) * 100)
  const failProgress = failedCourses <= graduationRequirements.maxFailedCourses ? 100 :
                      Math.max(0, 100 - ((failedCourses - graduationRequirements.maxFailedCourses) * 20))

  // 综合毕业进度（各项权重）
  const overallProgress = Math.round(
    (creditProgress * 0.4 +      // 学分完成度 40%
     gpaProgress * 0.3 +         // GPA达标度 30%
     courseProgress * 0.2 +      // 课程完成度 20%
     failProgress * 0.1) * 0.01  // 挂科控制 10%
  )

  return {
    excellent: stats.excellent,
    good: stats.good,
    average: stats.average,
    needImprovement: stats.fail + Math.max(0, failedCourses - graduationRequirements.maxFailedCourses),
    percentage: Math.min(100, overallProgress),
    details: {
      creditProgress: Math.round(creditProgress),
      gpaProgress: Math.round(gpaProgress),
      courseProgress: Math.round(courseProgress),
      failProgress: Math.round(failProgress),
      completedCredits,
      failedCourses,
      currentGPA: currentGPA.toFixed(2)
    }
  }
})

// 学分进度饼图样式
const creditProgressStyle = computed(() => {
  const percentage = creditProgress.value.percentage
  const angle = (percentage / 100) * 360

  return {
    background: `conic-gradient(
      #4caf50 0deg ${angle}deg,
      #e0e0e0 ${angle}deg 360deg
    )`
  }
})

// 毕业进度饼图样式 - 基于毕业要求各项指标
const graduationProgressStyle = computed(() => {
  const progress = graduationProgress.value

  if (!progress.details) {
    return {
      background: '#e0e0e0'
    }
  }

  // 各项指标的权重和角度
  const creditAngle = (progress.details.creditProgress / 100) * 144  // 40% 权重 = 144度
  const gpaAngle = (progress.details.gpaProgress / 100) * 108        // 30% 权重 = 108度
  const courseAngle = (progress.details.courseProgress / 100) * 72   // 20% 权重 = 72度
  const failAngle = (progress.details.failProgress / 100) * 36       // 10% 权重 = 36度

  let currentAngle = 0
  const segments = []

  // 学分进度段
  if (creditAngle > 0) {
    segments.push(`#4caf50 ${currentAngle}deg ${currentAngle + creditAngle}deg`)
    currentAngle += creditAngle
  }

  // GPA进度段
  if (gpaAngle > 0) {
    segments.push(`#2196f3 ${currentAngle}deg ${currentAngle + gpaAngle}deg`)
    currentAngle += gpaAngle
  }

  // 课程进度段
  if (courseAngle > 0) {
    segments.push(`#ff9800 ${currentAngle}deg ${currentAngle + courseAngle}deg`)
    currentAngle += courseAngle
  }

  // 挂科控制段
  if (failAngle > 0) {
    segments.push(`#9c27b0 ${currentAngle}deg ${currentAngle + failAngle}deg`)
    currentAngle += failAngle
  }

  // 未完成部分
  if (currentAngle < 360) {
    segments.push(`#e0e0e0 ${currentAngle}deg 360deg`)
  }

  return {
    background: segments.length > 0 ? `conic-gradient(${segments.join(', ')})` : '#e0e0e0'
  }
})

// 方法
const getScoreLevel = (score: number): string => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'average'
  if (score >= 60) return 'pass'
  return 'fail'
}

const getGradeLevel = (score: number): string => {
  if (score >= 95) return 'A+'
  if (score >= 90) return 'A'
  if (score >= 85) return 'A-'
  if (score >= 80) return 'B+'
  if (score >= 75) return 'B'
  if (score >= 70) return 'B-'
  if (score >= 65) return 'C+'
  if (score >= 60) return 'C'
  return 'F'
}

// 根据成绩计算绩点（按清华大学标准）
const getGPA = (score: number): number => {
  if (score >= 95) return 4.0  // A+
  if (score >= 90) return 4.0  // A
  if (score >= 85) return 3.7  // A-
  if (score >= 80) return 3.3  // B+
  if (score >= 75) return 3.0  // B
  if (score >= 70) return 2.7  // B-
  if (score >= 65) return 2.3  // C+
  if (score >= 60) return 2.0  // C
  return 0.0  // F
}

// 获取绩点等级
const getGPALevel = (gpa: number): string => {
  if (gpa >= 3.5) return 'excellent'
  if (gpa >= 3.0) return 'good'
  if (gpa >= 2.0) return 'average'
  return 'warning'
}

// 获取绩点等级文本
const getGPALevelText = (gpa: number): string => {
  if (gpa >= 3.5) return '优秀'
  if (gpa >= 3.0) return '良好'
  if (gpa >= 2.0) return '合格'
  return '需改进'
}

const formatDate = (date: Date): string => {
  return date.toLocaleDateString('zh-CN')
}

const resetFilters = () => {
  filters.semester = ''
  filters.course = ''
  filters.grade = ''
  filters.scoreRange = ''
  searchQuery.value = ''
  currentPage.value = 1
}

// 编辑成绩相关方法
const editGrade = (grade: Grade) => {
  if (!canInputGrades.value) {
    alert('权限不足！您没有成绩录入权限。')
    return
  }
  editingGrade.value = { ...grade }
  showEditDialog.value = true
}

const closeEditDialog = () => {
  showEditDialog.value = false
  editingGrade.value = {} as Grade
}

const calculateTotalScore = (): number => {
  const regular = editingGrade.value.regularScore || 0
  const midterm = editingGrade.value.midtermScore || 0
  const final = editingGrade.value.finalScore || 0

  // 总评成绩 = 平时成绩 × 30% + 期中成绩 × 30% + 期末成绩 × 40%
  const total = Math.round(regular * 0.3 + midterm * 0.3 + final * 0.4)
  return total
}

const saveGrade = () => {
  // 计算总评成绩
  editingGrade.value.totalScore = calculateTotalScore()
  editingGrade.value.grade = getGradeLevel(editingGrade.value.totalScore)

  // 更新成绩数据
  const index = grades.value.findIndex(g => g.id === editingGrade.value.id)
  if (index !== -1) {
    grades.value[index] = { ...editingGrade.value }
  }

  // 关闭对话框
  closeEditDialog()

  // 显示成功消息（这里可以集成消息提示组件）
  alert('成绩修改成功！')
}

// 删除成绩相关方法
const deleteGrade = (grade: Grade) => {
  if (!canManageGrades.value) {
    alert('权限不足！您没有成绩管理权限。')
    return
  }
  deletingGrade.value = grade
  showDeleteDialog.value = true
}

const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  deletingGrade.value = null
}

const confirmDelete = () => {
  if (deletingGrade.value) {
    const index = grades.value.findIndex(g => g.id === deletingGrade.value!.id)
    if (index !== -1) {
      grades.value.splice(index, 1)
    }

    // 显示成功消息
    alert('成绩删除成功！')
  }

  closeDeleteDialog()
}

const exportGrades = () => {
  if (!canExportData.value) {
    alert('权限不足！您没有数据导出权限。')
    return
  }
  const csvContent = generateCSVContent()
  downloadCSV(csvContent, `成绩报表_${new Date().toISOString().split('T')[0]}.csv`)
  alert('成绩数据导出成功！')
}

const batchImport = () => {
  if (!canManageGrades.value) {
    alert('权限不足！您没有成绩管理权限，无法进行批量导入操作。')
    return
  }
  alert('批量导入功能\n\n支持Excel/CSV文件格式\n请确保文件包含：学号、姓名、课程、成绩等字段')
}

const batchGradeEntry = () => {
  if (!canInputGrades.value) {
    alert('权限不足！您没有成绩录入权限，无法进行批量录入操作。')
    return
  }
  alert('批量录入功能\n\n可以为多个学生同时录入同一门课程的成绩\n支持按班级、专业批量操作')
}

const generateReports = () => {
  if (!checkPermission('report.view')) {
    alert('权限不足！您没有报表查看权限。')
    return
  }

  const stats = gradeStats.value
  const total = filteredGrades.value.length
  const avgGPA = averageGPA.value

  const reportContent = `
成绩统计报表
生成时间：${new Date().toLocaleString('zh-CN')}
总记录数：${total}

成绩分布：
- 优秀 (90-100分)：${stats.excellent} 人 (${((stats.excellent/total)*100).toFixed(1)}%)
- 良好 (80-89分)：${stats.good} 人 (${((stats.good/total)*100).toFixed(1)}%)
- 中等 (70-79分)：${stats.average} 人 (${((stats.average/total)*100).toFixed(1)}%)
- 及格 (60-69分)：${stats.pass} 人 (${((stats.pass/total)*100).toFixed(1)}%)
- 不及格 (<60分)：${stats.fail} 人 (${((stats.fail/total)*100).toFixed(1)}%)

平均绩点：${avgGPA.toFixed(2)}
学分完成进度：${creditProgress.value.percentage}%
  `

  alert(reportContent)
}

// 生成CSV内容
const generateCSVContent = () => {
  const headers = ['学号', '姓名', '课程', '学期', '平时成绩', '期中成绩', '期末成绩', '总评成绩', '等级', '绩点', '学分', '录入时间']
  const rows = filteredGrades.value.map(grade => [
    grade.studentId,
    grade.studentName,
    grade.courseName,
    grade.semester,
    grade.regularScore || '',
    grade.midtermScore || '',
    grade.finalScore || '',
    grade.totalScore,
    getGradeLevel(grade.totalScore),
    getGPA(grade.totalScore),
    grade.credits,
    formatDate(grade.inputTime)
  ])

  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

// 下载CSV文件
const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

onMounted(() => {
  // 初始化成绩数据
  console.log('正在生成5000名学生的成绩数据...')
  grades.value = generateGradesData()
  console.log(`成功生成 ${grades.value.length} 条成绩记录`)
})
</script>

<style scoped>
.grades-container {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.header-content p {
  color: #666;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-outline {
  background: white;
  color: #667eea;
  border: 1px solid #667eea;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.filter-select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.search-row {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.search-input-group {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 分析区域 */
.analytics-section {
  margin-bottom: 2rem;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.progress-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-icon.excellent {
  background: #e8f5e8;
}

.stat-icon.good {
  background: #e3f2fd;
}

.stat-icon.average {
  background: #fff3e0;
}

.stat-icon.pass {
  background: #f3e5f5;
}

.stat-icon.fail {
  background: #ffebee;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* 图表卡片 */
.chart-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.chart-card h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

/* 饼图容器 */
.pie-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.pie-chart {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-center {
  background: white;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.progress-label {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

/* 图例 */
.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.completed {
  background: #4caf50;
}

.legend-color.remaining {
  background: #e0e0e0;
}

.legend-color.excellent {
  background: #4caf50;
}

.legend-color.good {
  background: #2196f3;
}

.legend-color.average {
  background: #ff9800;
}

.legend-color.warning {
  background: #f44336;
}

.legend-total {
  margin-top: 0.5rem;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* GPA卡片 */
.gpa-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.gpa-card h3 {
  color: white;
}

.gpa-display-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.gpa-value {
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.gpa-value.excellent {
  color: #4caf50;
}

.gpa-value.good {
  color: #2196f3;
}

.gpa-value.average {
  color: #ff9800;
}

.gpa-value.warning {
  color: #f44336;
}

.gpa-level-text {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
}

.gpa-progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.gpa-progress-fill {
  height: 100%;
  background: white;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.gpa-scale {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 0.8rem;
  opacity: 0.8;
}

/* 绩点徽章 */
.gpa-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.gpa-badge.excellent {
  background: #e8f5e8;
  color: #2e7d32;
}

.gpa-badge.good {
  background: #e3f2fd;
  color: #1976d2;
}

.gpa-badge.average {
  background: #fff3e0;
  color: #f57c00;
}

.gpa-badge.pass {
  background: #f3e5f5;
  color: #7b1fa2;
}

.gpa-badge.fail {
  background: #ffebee;
  color: #d32f2f;
}

/* 成绩列表 */
.grades-list {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.list-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.list-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn:hover {
  background: #f8f9fa;
}

/* 表格 */
.table-container {
  overflow-x: auto;
}

.grades-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.grades-table th,
.grades-table td {
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.grades-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

.student-name {
  font-weight: 500;
  color: #333;
}

.total-score {
  text-align: center;
}

.score-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.score-badge.excellent {
  background: #e8f5e8;
  color: #2e7d32;
}

.score-badge.good {
  background: #e3f2fd;
  color: #1976d2;
}

.score-badge.average {
  background: #fff3e0;
  color: #f57c00;
}

.score-badge.pass {
  background: #f3e5f5;
  color: #7b1fa2;
}

.score-badge.fail {
  background: #ffebee;
  color: #d32f2f;
}

.grade-level {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.input-time {
  color: #666;
  font-size: 0.8rem;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

.table-actions .action-btn {
  padding: 0.25rem 0.5rem;
  min-width: auto;
}

.action-btn.edit:hover {
  background: #e3f2fd;
}

.action-btn.delete:hover {
  background: #ffebee;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.page-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grades-container {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-row {
    grid-template-columns: 1fr;
  }
  
  .search-row {
    flex-direction: column;
  }
  
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .list-header {
    flex-direction: column;
    gap: 1rem;
  }
}

/* 对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.small {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #666;
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e1e5e9;
}

/* 表单样式 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-input {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.readonly {
  background: #f8f9fa;
  color: #666;
  cursor: not-allowed;
}

.total-score-display {
  font-weight: 600;
  color: #333;
}

.form-hint {
  color: #666;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.warning-text {
  color: #e74c3c;
  font-weight: 500;
  margin-top: 0.5rem;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}
</style>
